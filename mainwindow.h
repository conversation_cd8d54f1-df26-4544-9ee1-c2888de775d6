#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QKeyEvent>
#include <QApplication>
#include <QScreen>
#include <QTimer>
#include <QMessageBox>
#include <QGuiApplication>
#include <QLabel>
#include <QDateTime>
#include <QInputDialog>
#include <QMenu>
#include <QAction>
#include <QContextMenuEvent>
#include <random>
#include <iostream>
#include <ctime>
#include <algorithm>

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
     void setupDVD();
     void moveDVD();
     void updateTimeDisplay();
     void checkPlatformSupport();
     void showCustomTextDialog();
     void toggleDVDMode();
     void toggleFullscreen();
     void updateFontSize();
     void resetTimer();
     void showCommentDialog();

protected:
    void contextMenuEvent(QContextMenuEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;

private:
    Ui::MainWindow *ui;
    QScreen *screen = QApplication::primaryScreen();
    QLabel *timeLabel;
    QTimer *displayTimer;
    QTimer *dvdTimer;
    bool canMoveWindow = true;
    bool dvdModeEnabled = true;
    bool isFullscreen = false;
    int elapsedSeconds = 0;
    QString customText = "Nothing to do";
    QString comment = "...";
    int normalFontSize = 18;
    int fullscreenFontSize = 72;

    double iconX, iconY;  // 浮動小数点で位置を管理
    double velocityX, velocityY;  // 速度ベクトル
    enum Vertical {UP,DOWN}vert;
    enum Horizontal {LEFT,RIGHT}hor;
};
#endif // MAINWINDOW_H
