#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QTime>

using namespace std;

MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent) , ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    checkPlatformSupport();
    setupDVD();

    // 時刻表示用のラベルを作成
    timeLabel = new QLabel(this);
    timeLabel->setAlignment(Qt::AlignCenter);
    setCentralWidget(timeLabel);

    // 初期フォントサイズを設定
    updateFontSize();

    // 時刻更新用タイマー
    displayTimer = new QTimer(this);
    connect(displayTimer, &QTimer::timeout, this, &MainWindow::updateTimeDisplay);
    displayTimer->start(1000); // 1秒ごとに更新
    updateTimeDisplay(); // 初期表示

    // DVDモード用タイマー
    dvdTimer = new QTimer(this);
    dvdTimer->setSingleShot(false);
    dvdTimer->start(16); // 約60FPSで更新（16ms間隔）
    connect(dvdTimer, &QTimer::timeout, this, &MainWindow::moveDVD);
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupDVD()
{
   vert = UP;
   hor = LEFT;
   QRect screenGeometry = screen->geometry();
   // シード値を設定
   srand(static_cast<unsigned int>(time(nullptr)));
   iconX = rand() % (screenGeometry.width() - width()) + 1;
   iconY = rand() % (screenGeometry.height() - height()) + 1;

   // DVDスクリーンセーバーのような速度を設定（2-4ピクセル/フレーム）
   velocityX = (rand() % 3 + 2) * ((rand() % 2 == 0) ? 1 : -1); // 2-4ピクセル、ランダムな方向
   velocityY = (rand() % 3 + 2) * ((rand() % 2 == 0) ? 1 : -1); // 2-4ピクセル、ランダムな方向
}

void MainWindow::updateTimeDisplay()
{
    int hours = elapsedSeconds / 3600;
    int minutes = (elapsedSeconds % 3600) / 60;
    int seconds = elapsedSeconds % 60;

    QString timeString = QString("%1:%2:%3")
        .arg(hours, 2, 10, QChar('0'))
        .arg(minutes, 2, 10, QChar('0'))
        .arg(seconds, 2, 10, QChar('0'));

    timeLabel->setText(customText + " at " + timeString + "\n" + comment);
    elapsedSeconds++;
}

void MainWindow::moveDVD()
{
   if (!canMoveWindow || !dvdModeEnabled) return;
   QRect screenGeometry = screen->geometry();

   // 次の位置を計算
   double nextX = iconX + velocityX;
   double nextY = iconY + velocityY;

   // 水平方向の境界判定と跳ね返り
   if (nextX <= 0) {
       nextX = 0;
       velocityX = -velocityX; // 速度を反転
   } else if (nextX >= screenGeometry.width() - width()) {
       nextX = screenGeometry.width() - width();
       velocityX = -velocityX; // 速度を反転
   }

   // 垂直方向の境界判定と跳ね返り
   if (nextY <= 0) {
       nextY = 0;
       velocityY = -velocityY; // 速度を反転
   } else if (nextY >= screenGeometry.height() - height()) {
       nextY = screenGeometry.height() - height();
       velocityY = -velocityY; // 速度を反転
   }

   // 位置を更新
   iconX = nextX;
   iconY = nextY;

   // ウィンドウを移動（整数値に変換）
   move(static_cast<int>(iconX), static_cast<int>(iconY));
}




void MainWindow::checkPlatformSupport()
{
    QString platformName = QGuiApplication::platformName();

    if (platformName == "wayland") {
        canMoveWindow = false;
        QMessageBox::warning(this, "Platform Warning",
            "Running on Wayland. Window positioning may not work properly.\n"
            "Consider running with: QT_QPA_PLATFORM=xcb ./ArrowKey\n"
            "or use X11 session for full functionality.");
    } else if (platformName == "xcb" || platformName == "x11") {
        canMoveWindow = true;
    } else {
        // 他のプラットフォームでは試してみる
        canMoveWindow = true;
    }
}

void MainWindow::contextMenuEvent(QContextMenuEvent *event)
{
    QMenu contextMenu(this);

    QAction *changeTextAction = new QAction("テキストを変更 (T)", this);
    connect(changeTextAction, &QAction::triggered, this, &MainWindow::showCustomTextDialog);
    contextMenu.addAction(changeTextAction);

    QAction *changeCommentAction = new QAction("コメントを変更 (C)", this);
    connect(changeCommentAction, &QAction::triggered, this, &MainWindow::showCommentDialog);
    contextMenu.addAction(changeCommentAction);

    QAction *resetTimerAction = new QAction("タイマーをリセット (R)", this);
    connect(resetTimerAction, &QAction::triggered, this, &MainWindow::resetTimer);
    contextMenu.addAction(resetTimerAction);

    contextMenu.addSeparator();

    QAction *toggleDVDAction = new QAction(dvdModeEnabled ? "DVDモードを無効にする (D)" : "DVDモードを有効にする (D)", this);
    connect(toggleDVDAction, &QAction::triggered, this, &MainWindow::toggleDVDMode);
    contextMenu.addAction(toggleDVDAction);

    QAction *toggleFullscreenAction = new QAction(isFullScreen() ? "ウィンドウモードに戻す (F)" : "フルスクリーンにする (F)", this);
    connect(toggleFullscreenAction, &QAction::triggered, this, &MainWindow::toggleFullscreen);
    contextMenu.addAction(toggleFullscreenAction);

    contextMenu.exec(event->globalPos());
}

void MainWindow::showCustomTextDialog()
{
    bool ok;
    QString text = QInputDialog::getText(this, "カスタムテキスト",
                                         "表示するテキストを入力してください:",
                                         QLineEdit::Normal,
                                         customText, &ok);
    if (ok && !text.isEmpty()) {
        customText = text;
        updateTimeDisplay(); // 表示を即座に更新
    }
}

void MainWindow::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
    case Qt::Key_D:
        toggleDVDMode();
        break;
    case Qt::Key_F:
        toggleFullscreen();
        break;
    case Qt::Key_R:
        resetTimer();
        break;
    case Qt::Key_T:
        showCustomTextDialog();
        break;
    case Qt::Key_C:
        showCommentDialog();
        break;
    default:
        QMainWindow::keyPressEvent(event);
        break;
    }
}

void MainWindow::toggleDVDMode()
{
    dvdModeEnabled = !dvdModeEnabled;
    if (dvdModeEnabled) {
        // DVDモードを有効にする
        setupDVD(); // 位置と速度をリセット
        dvdTimer->start(16);
    } else {
        // DVDモードを無効にする
        dvdTimer->stop();
    }
}

void MainWindow::toggleFullscreen()
{
    if (isFullScreen()) {
        // ウィンドウモードに戻す
        showNormal();
        // 元のサイズ制約を復元
        setMinimumSize(280, 150);
        setMaximumSize(280, 150);
        isFullscreen = false;
    } else {
        // フルスクリーンモードにする
        // サイズ制約を解除
        setMinimumSize(0, 0);
        setMaximumSize(16777215, 16777215); // Qt's maximum widget size
        showFullScreen();
        isFullscreen = true;
    }

    // フォントサイズを更新
    updateFontSize();
}

void MainWindow::updateFontSize()
{
    int fontSize = isFullScreen() ? fullscreenFontSize : normalFontSize;
    QString labelStyleSheet = QString("QLabel { "
                                    "font-size: %1px; "
                                    "font-weight: bold; "
                                    "}").arg(fontSize);

    timeLabel->setStyleSheet(labelStyleSheet);
}

void MainWindow::resetTimer()
{
    elapsedSeconds = 0;
    updateTimeDisplay(); // 表示を即座に更新
}

void MainWindow::showCommentDialog()
{
    bool ok;
    QString text = QInputDialog::getText(this, "コメント",
                                         "コメントを入力してください:",
                                         QLineEdit::Normal,
                                         comment, &ok);
    if (ok) {
        comment = text;
        updateTimeDisplay(); // 表示を即座に更新
    }
}
